---
import LayoutDefault from '~/layouts/LayoutDefault.astro'
import Post from '~/components/Post.astro'
import { getPosts, getPostDescription } from '~/utils'
import Pagination from '~/components/Pagination.astro'

const { pagination } = Astro.locals.config
const posts = (await getPosts()).slice(0, pagination.postsPerPage)

const { translate: t } = Astro.locals

---

<LayoutDefault>
  <section contain-layout un-flex="~ col gap-7.5">
    {
      posts.map((post) => (
        <Post post={post}>
          <p class="line-clamp-4">{getPostDescription(post)}</p>
        </Post>
      ))
    }
    <Pagination
      showLeft={false}
      showRight={true}
      rightTitle={t('next')}
      rightUrl={'posts/page/2'}
      currentPage={1}
      totalPage={Math.ceil((await getPosts()).length / pagination.postsPerPage)}
      baseUrl="/posts/page/"
      showPageCount={false}
    />
  </section>
</LayoutDefault>
